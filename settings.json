{"theme": "De<PERSON>ult Light", "selectedAuthType": "oauth-personal", "preferredEditor": "vscode", "mcpServers": {"mssql-Royalbase": {"command": "pwsh.exe", "args": ["-NoProfile", "-ExecutionPolicy", "Bypass", "-Command", "cd \"D:/mcp-servers/mssql_mcp_server\"; $env:MSSQL_SERVER=\"LJNB015\"; $env:MSSQL_USER=\"mcp_login\"; $env:MSSQL_PASSWORD=\"P+7?);/}u.sv``<*,2ArZk4\"; $env:MSSQL_DATABASE=\"RBMS\"; uv run mssql_mcp_server"]}}}