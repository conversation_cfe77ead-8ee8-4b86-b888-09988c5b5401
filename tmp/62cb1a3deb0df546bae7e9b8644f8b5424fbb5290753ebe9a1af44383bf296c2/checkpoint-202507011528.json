[{"role": "user", "parts": [{"text": "This is the Gemini CLI. We are setting up the context for our chat.\n  Today's date is Tuesday, July 1, 2025.\n  My operating system is: linux\n  I'm currently working in the directory: /mnt/e/tfs/PJ2017031_RB\n  Showing up to 200 items (files + folders). Folders or files indicated with ... contain more items not shown, were ignored, or the display limit (200 items) was reached.\n\n/mnt/e/tfs/PJ2017031_RB/\n├───.clineignore\n├───.cursorignore\n├───.cursorrules\n├───.env.example\n├───.gitattributes\n├───.gitignore\n├───.mcp.json\n├───.monicaignore\n├───.roomodes\n├───.windsurfrules\n├───CHANGELOG.md\n├───CLAUDE.md\n├───codex.md\n├───GEMINI.md\n├───README.md\n├───RoyalBase.sln\n├───.ai-requests/\n│   ├───20250613-種苗需求單批次管理頁面優化需求.md\n│   └───20250614-種苗需求單批次管理頁面優化需求.md\n├───.claude/\n│   └───settings.json\n├───.cursor/\n│   ├───environment.json\n│   ├───mcp.json\n│   └───rules/\n├───.git/...\n├───.github/\n│   ├───.copilot-codeGeneration-instructions.md\n│   ├───.copilot-commit-message-instructions.md\n│   ├───.copilot-generate-changelog-instruction.md\n│   ├───.copilot-pull-request-description-instructions.md\n│   ├───.copilot-review-instructions.md\n│   ├───copilot-instructions.md\n│   ├───project-architecture.md\n│   ├───chatmodes/\n│   └───workflows/\n├───.roo/\n│   └───rules-architect/\n├───.taskmaster/\n│   ├───config.json\n│   ├───state.json\n│   └───templates/\n├───.vs/\n│   └───RoyalBase/...\n├───.vscode/\n│   ├───settings.json\n│   ├───sync-uc-web-docs.ps1\n│   └───tasks.json\n├───ai-sessions/\n│   ├───20240726-0313-專案驗證模式分析與架構指南強化.md\n│   ├───2025-0611-0000-需求單管理編輯頁按鈕功能與排序優化.md\n│   ├───20250117-git-merge-conflict-resolution.md\n│   ├───20250610-1357-種苗需求單提交按鈕儲存功能修復與代碼重構優化.md\n│   ├───20250611-1437-解決MenuResource編譯錯誤CS0117.md\n│   ├───20250611-1459-建立copilot-fix-32分支worktree與設定檔案複製.md\n│   ├───20250611-1549-建立種苗需求單功能模組完整Pull Request訊息.md\n│   ├───20250611-1630-新增多國語系資源鍵苗與梗.md\n│   ├───20250612-1052-重構批次管理頁面的出貨日期選擇器.md\n│   ├───20250612-1338-修復JavaScript多語系資源參考錯誤.md\n│   ├───20250612-1338-種苗需求單BatchManage頁面FieldResource未定義錯誤修復.md\n│   ├───20250612-1400-前端後端驗證錯誤顯示機制研究.md\n│   ├───20250612-1410-修復種苗需求單批次管理頁面日期欄位驗證的UI顯示問題.md\n│   ├───20250612-1504-分析BatchManage與通用錯誤處理機制並提出整合方案.md\n│   ├───20250612-1615-修復種苗需求單批次管理API的Entity Framework錯誤.md\n│   ├───20250612-1630-修復種苗需求單批次管理頁面JavaScript狀態檢查錯誤.md\n│   ├───20250612-1639-種苗需求單B2B訂單號搜尋修復.md\n│   ├───20250612-1639-種苗需求單批次管理JS程式碼修正與優化.md\n│   ├───20250612-2000-種苗需求單批次管理頁面調整與debounce函式修正.md\n│   ├───20250612-2015-移除種苗需求單獨立搜尋欄位.md\n│   ├───20250612-2130-種苗需求單驗證錯誤顯示修正.md\n│   ├───20250613-0916-修正種苗需求單批次管理燈箱JavaScript錯誤.md\n│   ├───20250613-1115-種苗需求單AddProductLightbox等級下拉選單修復與JS重構.md\n│   ├───20250613-1200-種苗需求單批次管理燈箱錯誤修正與功能重構.md\n│   ├───20250613-2004-種苗需求單批次管理矩陣表格欄位結構修復.md\n│   ├───20250614-0900-種苗需求單批次管理頁面UI優化.md\n│   ├───20250614-1000-種苗需求單批次管理頁面固定欄位優化.md\n│   ├───20250615-1800-專案檔案組織規則統一更新.md\n│   ├───20250615-1800-需求挑苗排程前期文件拆分.md\n│   ├───20250615-1930-需求挑苗排程資料庫設計與SQL腳本優化.md\n│   ├───20250615-1930-需求挑苗排程資料庫設計與SQL腳本規範化.md\n│   ├───20250615-2030-需求挑苗排程資料庫設計與實作.md\n│   ├───20250615-2220-需求挑苗排程SourceOrderDetailIDs設計決策分析.md\n│   ├───20250615-2245-需求挑苗排程Log表觸發器SQL執行錯誤修正.md\n│   ├───20250616-0220-修復資料庫升級腳本因rowversion處理不當導致的錯誤.md\n│   ├───20250617-1530-需求挑苗排程Controller設計與實作.md\n│   ├───20250617-1850-種苗需求單批次管理梗苗欄位改為下拉選單.md\n│   ├───20250617-1920-種苗需求單批次管理梗苗欄位修正與自動儲存功能移除.md\n│   ├───20250617-2001-RoyalBase-MVC-Controller-實作指南文件準確性檢查與修訂.md\n│   ├───20250618-0013-需求挑苗排程ViewModels設計檢視.md\n│   ├───20250618-0130-Service層實作指南驗證與修正.md\n│   ├───20250619-1145-需求挑苗排程Controller實作與CodeReview修正.md\n│   ├───20250619-1720-新增需求品項燈箱表單驗證問題修復.md\n│   ├───20250619-1743-新增需求品項燈箱梗苗欄位預設值修復.md\n│   ├───20250619-1854-Shell腳本XML插入邏輯修正與資源檔案修復.md\n│   ├───20250619-2126-種苗需求挑苗排程Controller編譯錯誤修復與依賴注入重構.md\n│   ├───20250619-2216-需求挑苗排程Controller深度檢查與安全性修正.md\n│   ├───20250620-0057-確認時不應該打api前端邏輯修復.md\n│   ├───20250620-0117-修復種苗需求單批次管理頁的儲存功能錯誤.md\n│   ├───20250620-0130-修復種苗需求單列表總需求量計算錯誤.md\n│   ├───20250620-0153-修復種苗需求單批次管理儲存問題.md\n│   ├───20250620-0211-修正種苗需求單批次管理總需求量顯示問題.md\n│   ├───20250620-0211-新增多國語系資源鍵總需求.md\n│   ├───20250621-2246-ASP.NET-Web.config重複參考建置錯誤排除.md\n│   ├───20250622-1600-需求挑苗排程方法定義一致性檢查與修正.md\n│   ├───20250622-1729-YPOrderPickingSchedule文件檢查與修正.md\n│   ├───20250622-1756-Service層實作指南濃縮版製作.md\n│   ├───20250622-2250-解決YPOrderPickingSchedule中提交按鈕的衝突與標準化.md\n│   ├───20250622-2350-分析並修正Controller與專案框架的隱性衝突.md\n│   ├───20250623-0246-需求挑苗排程服務編譯錯誤修復.md\n│   ├───20250623-0248-修復需求挑苗排程驗證邏輯與服務層重構.md\n│   ├───20250623-0300-CommitSchedules方法重構與資料模型修正.md\n│   ├───20250623-1000-RoyalBase專案CS檔案中文摘要批次新增作業.md\n│   ├───20250623-1200-Validation類別實作指南完善與修正.md\n│   ├───20250623-1243-YPOrderPickingScheduleValidation實作計畫與完成.md\n│   ├───20250623-1400-YPOrderPickingScheduleValidation編譯錯誤修復.md\n│   ├───20250623-1400-皇基系統Fancybox深度研究與完整實作指南.md\n│   ├───20250623-1605-需求挑苗排程前端開發計劃制定.md\n│   ├───20250623-2130-需求挑苗排程列表頁實作與檢查.md\n│   ├───20250625-1500-需求挑苗排程Service實作與術語重構.md\n│   ├───20250626-1420-TFS操作實踐與文檔更新.md\n│   ├───20250627-0704-page-title自動顯示機制分析與文件建立.md\n│   ├───20250628-2240-釐清需求挑苗排程的提交邏輯與職責轉移.md\n│   ├───20250628-2302-徹底移除需求挑苗排程文件中對YPPicking的不當引用.md\n│   ├───20250628-2302-新增挑苗作業資料表與提交邏輯重構.md\n│   ├───20250629-2000-需求挑苗排程修改頁面UI修正.md\n│   ├───20250629-2112-修復需求挑苗排程新建錯誤與簡化架構設計.md\n│   └───YP_UC_Relationships_Analysis.md\n├───DBUpgradeTool/\n│   ├───App.config\n│   ├───CLAUDE.md\n│   ├───DBUpgradeTool.csproj\n│   ├───packages.config\n│   ├───Program.cs\n│   ├───.vs/\n│   ├───bin/\n│   ├───obj/\n│   └───Properties/\n├───Docs/\n│   ├───CLAUDE.md\n│   ├───Fancybox_Business_Templates.md\n│   ├───Fancybox_Core_Architecture.md\n│   ├───.git/...\n│   ├───104594/\n│   ├───104723_104727_104728_104737_品種報價類別/\n│   ├───104766_104767_104772_集團價格管理/\n│   ├───105086_105087_105088_105089_花色報價類別/\n│   ├───105463/\n│   ├───105465/\n│   ├───105599/\n│   ├───106135/\n│   ├───106281_種苗需求單角色權限/\n│   ├───106283_種苗需求單列表頁/\n│   ├───107340-需求挑苗排程/\n│   ├───instructions/\n│   ├───prompts/\n│   ├───Templates/\n│   ├───toolbar-button-guide/\n│   ├───UC_APP/\n│   ├───UC_WEB/\n│   └───客戶報價管理_105107_105108_105109_105110_105111/\n├───logs/\n├───packages/\n│   ├───Antlr.3.5.0.2/...\n│   ├───Azure.Core.1.38.0/...\n│   ├───Azure.Identity.1.11.4/...\n│   ├───bootstrap.4.1.3/...\n│   ├───Castle.Core.4.4.0/...\n│   ├───Castle.Core.5.1.1/...\n│   ├───Elastic.Apm.1.21.0/...\n│   ├───Elastic.Apm.AspNetFullFramework.1.21.0/...\n│   ├───Elastic.Apm.EntityFramework6.1.21.0/...\n│   ├───EntityFramework.6.2.0/...\n│   ├───EntityFramework.zh-Hant.6.2.0/...\n│   ├───EPPlus.4.5.2.1/...\n│   ├───EPPlus.6.2.1/...\n│   ├───jose-jwt.2.4.0/...\n│   ├───jQuery.3.3.1/...\n│   ├───jQuery.Validation.1.17.0/...\n│   ├───Microsoft.ApplicationInsights.2.8.0/...\n│   ├───Microsoft.ApplicationInsights.Agent.Intercept.2.4.0/...\n│   ├───Microsoft.ApplicationInsights.DependencyCollector.2.8.0/...\n│   ├───Microsoft.ApplicationInsights.PerfCounterCollector.2.8.0/...\n│   ├───Microsoft.ApplicationInsights.Web.2.8.0/...\n│   ├───Microsoft.ApplicationInsights.WindowsServer.2.8.0/...\n│   ├───Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.8.0/...\n│   ├───Microsoft.AspNet.Mvc.5.2.7/...\n│   ├───Microsoft.AspNet.Mvc.zh-Hant.5.2.7/...\n│   ├───Microsoft.AspNet.Razor.3.2.7/...\n│   ├───Microsoft.AspNet.Razor.zh-Hant.3.2.7/...\n│   ├───Microsoft.AspNet.TelemetryCorrelation.1.0.4/...\n│   ├───Microsoft.AspNet.Web.Optimization.1.1.3/...\n│   ├───Microsoft.AspNet.Web.Optimization.zh-Hant.1.1.3/...\n│   ├───Microsoft.AspNet.WebApi.5.2.6/...\n│   ├───Microsoft.AspNet.WebApi.5.2.7/...\n│   ├───Microsoft.AspNet.WebApi.Client.5.2.7/...\n│   └───...\n├───RoyalBase/\n├───RoyalBase.Tests/\n├───scripts/\n├───tests/\n└───tmp/"}]}, {"role": "model", "parts": [{"text": "Got it. Thanks for the context!"}]}, {"role": "user", "parts": [{"text": "目前在 /BSRole/Index 的頁面，對角色的功能權限做修正後，所有當下登入的使用者是沒有套用到權限的更新的，必需要登出後，再登入，才會套上新的權限\n\n  我要你做一個深度研究，看看要怎麼實現，一但功能權限更新，登入中的使用者就馬上套用上\n\n給我計劃，不要修改任何 CODE, 請盡量的了解整個專案"}]}, {"role": "model", "parts": [{"text": "好的，這是一個典型的權限管理系統優化需求。為了實現角色權限變更後即時生效，而不需要使用者重新登入，我需要先深入了解目前的權限驗證流程。這需要一個謹慎的分析，以確保解決方案高效且不會引入新問題。\n\n這是一個深度研究，我將完全遵守您的要求：**只提供計畫，不修改任何程式碼**。\n\n### 研究與分析計畫\n\n我的計畫分為兩個階段：**第一階段是深度分析**，以完全理解現有系統的運作方式；**第二階段是提出可行的解決方案**，並評估其優劣。\n\n---\n\n#### **第一階段：深度分析現有權限系統**\n\n目前的行為（需重新登入才生效）強烈暗示權限是在使用者登入時被讀取，並儲存在伺服器的 Session 或類似的快取機制中。我的分析將驗證這個假設，並找出所有相關的程式碼。\n\n1.  **分析權限檢查機制 (`MvcAuthFilter` & `MvcButtonFilter`)**\n    *   **目標**：確認系統如何在每個請求中檢查使用者的功能和按鈕權限。\n    *   **方法**：我會使用 `glob` 找到 `MvcAuthFilter.cs` 和 `MvcButtonFilter.cs` 檔案，並用 `read_file` 仔細閱讀其原始碼。\n    *   **分析重點**：\n        *   權限資料是從哪裡讀取的？（例如：`HttpContext.Session`）\n        *   檢查的邏輯是什麼？\n        *   是否有任何快取機制？\n\n2.  **分析權限載入時機 (Login Controller)**\n    *   **目標**：找到使用者登入時，系統將權限載入 Session 的確切位置。\n    *   **方法**：我會搜尋專案中處理登入邏輯的 Controller（通常可能是 `AccountController` 或類似名稱），並閱讀其 `Login` 的 `[HttpPost]` Action。\n    *   **分析重點**：\n        *   在哪個 Service 被呼叫以取得使用者的角色與權限資料？\n        *   權限資料是以何種格式被存入 Session？\n\n3.  **分析權限儲存邏輯 (`BSRoleController`)**\n    *   **目標**：了解在 `BSRole/Index` 頁面儲存權限變更時，後端發生了什麼事。\n    *   **方法**：我會找到 `BSRoleController.cs`，並分析其儲存權限的 Action（例如 `Save`, `Update`, `Edit`）。\n    *   **分析重點**：\n        *   它呼叫了哪個 Service 來更新資料庫？\n        *   更新過程中，是否有任何清除快取的相關邏輯？（目前看來是沒有，但需要確認）\n\n4.  **分析系統核心與基底類別 (`BaseController`, `ServiceBase`)**\n    *   **目標**：檢查基底類別中是否有與使用者 Session 或權限相關的通用邏輯。\n    *   **方法**：閱讀 `BaseController.cs` 和 `ServiceBase.cs` 的程式碼。\n    *   **分析重點**：\n        *   是否有通用的屬性或方法來存取目前登入使用者的資訊？\n        *   這些基底類別是否影響了權限檢查的流程？\n\n---\n\n#### **第二階段：提出解決方案與評估**\n\n在完成第一階段的分析後，我將對現有架構有完整的理解。屆時，我會根據分析結果，提出幾種可能的解決方案，並詳細說明其優缺點，供您選擇。\n\n可能的解決方案方向可能包含：\n\n1.  **方案一：每次請求重新查詢資料庫**\n    *   **說明**：修改 `MvcAuthFilter`，使其在每次需要驗證權限時，都直接從資料庫查詢最新的權限設定，而不是從 Session 讀取。\n    *   **優點**：最簡單直接，保證即時性。\n    *   **缺點**：對資料庫造成巨大壓力，每個 HTTP 請求都可能伴隨多次資料庫查詢，嚴重影響系統效能，**通常不建議**。\n\n2.  **方案二：使用快取時間戳記 (Timestamp/RowVersion)**\n    *   **說明**：\n        1.  在角色權限相關的資料表（例如 `BSRoleFunction`）增加一個 `LastModifiedDate` 時間戳記欄位，或使用資料庫既有的 `rowversion` 機制。\n        2.  使用者登入時，將這個時間戳記存入 Session。\n        3.  `MvcAuthFilter` 在每次請求時，用極小的成本查詢資料庫中角色的最新時間戳記，與 Session 中的版本比對。\n        4.  如果不一致，表示權限已被修改，此時才重新從資料庫載入完整權限並更新 Session。\n    *   **優點**：效能極佳，結合了即時性與低資料庫負載。\n    *   **缺點**：可能需要微調資料庫結構。\n\n3.  **方案三：全域應用程式快取 (Application Cache)**\n    *   **說明**：\n        1.  將所有角色的權限資料載入到一個全域的、所有使用者共享的應用程式級快取中（例如 `HttpRuntime.Cache`）。\n        2.  `MvcAuthFilter` 直接從這個高速的全域快取中讀取權限。\n        3.  當 `BSRoleController` 儲存權限變更時，它需要同時更新資料庫和這個全域快取。\n    *   **優點**：速度非常快，因為完全在記憶體中操作。\n    *   **缺點**：需要謹慎處理執行緒安全（多使用者同時讀寫）。在多伺服器部署（Web Farm）環境下，需要改用分散式快取（如 Redis），增加了架構複雜度。\n\n4.  **方案四：發布/訂閱模型 (Publish/Subscribe)**\n    *   **說明**：這是一個更現代化的作法。\n        1.  當 `BSRoleController` 更新權限時，它會發布一個「權限已變更」的訊息。\n        2.  可以透過 SignalR 或其他訊息佇列（Message Queue）技術，將此訊息推播給所有在線的使用者。\n        3.  前端或後端 Session 收到訊息後，主動清除自己的舊權限快取，下次請求時就會自動載入新的。\n    *   **優點**：最即時、最優雅的解決方案，擴展性好。\n    *   **缺點**：導入新技術（如 SignalR），專案的複雜度會顯著增加。\n\n---\n\n### 下一步\n\n我的下一步是開始執行上述的**第一階段：深度分析計畫**。我會使用 `glob` 和 `read_file` 工具來分析相關的程式碼，並在分析完成後，向您報告我的發現，並根據發現建議最適合本專案的具體實施方案。\n\n請問您是否同意我按照此計畫開始進行分析？"}]}, {"role": "user", "parts": [{"text": "好的，你先做好第一階段"}]}, {"role": "model", "parts": [{"text": "好的，我將開始執行第一階段的分析計畫。\n\n首先，我需要定位所有與權限、登入和角色管理相關的核心檔案。我將使用 `glob` 工具來搜尋這些檔案。這包括：\n\n*   `MvcAuthFilter.cs` 和 `MvcButtonFilter.cs`：權限檢查的核心。\n*   `AccountController.cs` 或類似檔案：處理使用者登入。\n*   `BSRoleController.cs`：角色權限的儲存點。\n*   `BaseController.cs` 和 `ServiceBase.cs`：可能包含通用邏輯的基底類別。\n\n我將並行執行這些搜尋以節省時間。"}]}, {"role": "model", "parts": [{"functionCall": {"name": "glob", "args": {"pattern": "**/MvcAuthFilter.cs"}}}, {"functionCall": {"name": "glob", "args": {"pattern": "**/MvcButtonFilter.cs"}}}, {"functionCall": {"name": "glob", "args": {"pattern": "**/AccountController.cs"}}}, {"functionCall": {"name": "glob", "args": {"pattern": "**/BSRoleController.cs"}}}, {"functionCall": {"name": "glob", "args": {"pattern": "**/BaseController.cs"}}}, {"functionCall": {"name": "glob", "args": {"pattern": "**/ServiceBase.cs"}}}]}, {"role": "user", "parts": [{"functionResponse": {"id": "glob-*************-1530da0ec0647", "name": "glob", "response": {"output": "Found 1 file(s) matching \"**/MvcAuthFilter.cs\" within /mnt/e/tfs/PJ2017031_RB, sorted by modification time (newest first):\n/mnt/e/tfs/PJ2017031_RB/RoyalBase/Filters/MvcAuthFilter.cs"}}}, {"functionResponse": {"id": "glob-*************-47c2bca9ded17", "name": "glob", "response": {"output": "Found 1 file(s) matching \"**/MvcButtonFilter.cs\" within /mnt/e/tfs/PJ2017031_RB, sorted by modification time (newest first):\n/mnt/e/tfs/PJ2017031_RB/RoyalBase/Filters/MvcButtonFilter.cs"}}}, {"functionResponse": {"id": "glob-*************-c6cd31287efdc", "name": "glob", "response": {"output": "No files found matching pattern \"**/AccountController.cs\" within /mnt/e/tfs/PJ2017031_RB."}}}, {"functionResponse": {"id": "glob-*************-d8a7b0b350fb", "name": "glob", "response": {"output": "Found 1 file(s) matching \"**/BSRoleController.cs\" within /mnt/e/tfs/PJ2017031_RB, sorted by modification time (newest first):\n/mnt/e/tfs/PJ2017031_RB/RoyalBase/Controllers/BS/BSRoleController.cs"}}}, {"functionResponse": {"id": "glob-*************-e0e21dfd1126c", "name": "glob", "response": {"output": "Found 1 file(s) matching \"**/BaseController.cs\" within /mnt/e/tfs/PJ2017031_RB, sorted by modification time (newest first):\n/mnt/e/tfs/PJ2017031_RB/RoyalBase/Core/MVC/BaseController.cs"}}}, {"functionResponse": {"id": "glob-*************-a90009228b70e", "name": "glob", "response": {"output": "Found 1 file(s) matching \"**/ServiceBase.cs\" within /mnt/e/tfs/PJ2017031_RB, sorted by modification time (newest first):\n/mnt/e/tfs/PJ2017031_RB/RoyalBase/Service/Common/ServiceBase.cs"}}}]}, {"role": "model", "parts": [{"text": "好的，檔案定位成功。\n\n我找到了以下關鍵檔案：\n*   `RoyalBase/Filters/MvcAuthFilter.cs`\n*   `RoyalBase/Filters/MvcButtonFilter.cs`\n*   `RoyalBase/Controllers/BS/BSRoleController.cs`\n*   `RoyalBase/Core/MVC/BaseController.cs`\n*   `RoyalBase/Service/Common/ServiceBase.cs`\n\n沒有找到 `AccountController.cs`，這表示登入功能的 Controller 可能有不同的命名。我將使用更廣泛的模式來尋找它，例如搜尋檔名包含 \"Login\" 的 Controller。\n\n現在，我將開始閱讀這些已找到的檔案內容，以分析現有的權限系統實作。"}]}, {"role": "model", "parts": [{"functionCall": {"name": "glob", "args": {"pattern": "**/*Login*Controller.cs"}}}, {"functionCall": {"name": "read_many_files", "args": {"paths": ["/mnt/e/tfs/PJ2017031_RB/RoyalBase/Filters/MvcAuthFilter.cs", "/mnt/e/tfs/PJ2017031_RB/RoyalBase/Filters/MvcButtonFilter.cs", "/mnt/e/tfs/PJ2017031_RB/RoyalBase/Controllers/BS/BSRoleController.cs", "/mnt/e/tfs/PJ2017031_RB/RoyalBase/Core/MVC/BaseController.cs", "/mnt/e/tfs/PJ2017031_RB/RoyalBase/Service/Common/ServiceBase.cs"]}}}]}, {"role": "user", "parts": [{"functionResponse": {"id": "glob-*************-9614ebb670279", "name": "glob", "response": {"output": "No files found matching pattern \"**/*Login*Controller.cs\" within /mnt/e/tfs/PJ2017031_RB."}}}, {"functionResponse": {"id": "read_many_files-*************-38d6345765a77", "name": "read_many_files", "response": {"output": "Tool execution succeeded."}}}, {"text": "--- RoyalBase/Controllers/BS/BSRoleController.cs ---\n\n// 檔案摘要：\n// 角色權限管理控制器，負責系統的角色定義與權限配置。本控制器提供角色的完整 CRUD 功能，支援角色名稱、說明、權限項目等設定。透過 RoleService 服務層處理複雜的權限繼承與組合邏輯。實作細緻的功能權限控管，包含查詢、新增、修改、刪除等操作權限。角色管理是系統安全的核心機制，確保使用者只能存取授權的功能。\n//\n\nusing Core.Extentions;\nusing Core.MVC;\nusing Core.MVC.ViewModels;\nusing RoyalBase.Enum;\nusing RoyalBase.Filters;\nusing RoyalBase.Resources;\nusing RoyalBase.Service;\nusing RoyalBase.Tools;\nusing RoyalBase.ViewModels;\nusing System.Collections.Generic;\nusing System.Linq;\nusing System.Web.Mvc;\nusing System.Web.Script.Serialization;\nusing RoyalBase.Validation;\nusing AuthToken = RoyalBase.AuthCode.WEB.BI.BIBS.BSRole;\n\n\nnamespace RoyalBase.Controllers\n{\n    public class BSRoleController : BaseController\n    {\n        private RoleService _RoleService = new RoleService();\n        private RoleValidation _RoleValidation = new RoleValidation();\n\n        #region 列表\n        /// <summary>\n        /// 列表頁\n        /// </summary>\n        /// <returns></returns>\n        [MvcAuthFilter(AuthToken.Query)]\n        public ActionResult Index()\n        {\n            var view = new BSRoleIndex();\n            return View(view);\n        }\n\n        #endregion\n\n        #region 增/刪/修\n        /// <summary>\n        /// 新增角色\n        /// </summary>\n        /// <param name=\"name\"></param>\n        /// <returns></returns>\n        [HttpPost]\n        [MvcAuthFilter(AuthToken.Edit)]\n        public JsonResult CreateRole(string name)\n        {\n            _RoleValidation.ValidateRole(name, ModelState);\n            var newRole =  _RoleService.Create(name);\n\n            // 回傳成功訊息\n            var response = new ApiJsonResponse\n            {\n                Msg = MessageResource.儲存成功,\n                Status = ApiResponseStatusEnum.Success,\n                JsonData = new JavaScriptSerializer().Serialize(newRole)\n            };\n\n            return Json(response, JsonRequestBehavior.AllowGet);\n        }\n\n        /// <summary>\n        /// 修改角色名稱\n        /// </summary>\n        /// <param name=\"id\"></param>\n        /// <param name=\"name\"></param>\n        /// <returns></returns>\n        [HttpPost]\n        [MvcAuthFilter(AuthToken.Edit)]\n        public JsonResult ModifyRole(int id, string name)\n        {\n            _RoleValidation.ValidateRole(name, ModelState);\n            _RoleService.Modify(id, name);\n\n            // 回傳成功訊息\n            var response = new ApiResponse\n            {\n                Msg = MessageResource.儲存成功,\n                Status = ApiResponseStatusEnum.Success\n            };\n\n            return Json(response, JsonRequestBehavior.AllowGet);\n        }\n\n        /// <summary>\n        /// 刪除角色\n        /// </summary>\n        /// <param name=\"id\"></param>\n        /// <returns></returns>\n        [HttpPost]\n        [MvcAuthFilter(AuthToken.Delete)]\n        public JsonResult DeleteRole(int id)\n        {\n            _RoleValidation.ValidateRoleDelete(id, ModelState);\n            _RoleService.Delete(id);\n\n            // 回傳成功訊息\n            var response = new ApiResponse\n            {\n                Msg = MessageResource.刪除成功,\n                Status = ApiResponseStatusEnum.Success\n            };\n\n            return Json(response, JsonRequestBehavior.AllowGet);\n        }\n\n        /// <summary>\n        /// 修改權限\n        /// </summary>\n        /// <param name=\"roleId\"></param>\n        /// <param name=\"tokens\"></param>\n        /// <returns></returns>\n        [HttpPost]\n        [MvcAuthFilter(AuthToken.Edit)]\n        public JsonResult SavePermission(int roleId, List<string> tokens)\n        {\n            _RoleService.SavePermission(roleId, tokens);\n            \n            // 回傳成功訊息\n            var response = new ApiResponse\n            {\n                Msg = MessageResource.儲存成功,\n                Status = ApiResponseStatusEnum.Success\n            };\n            return Json(response, JsonRequestBehavior.AllowGet);\n        }\n\n        #endregion\n\n        #region Ajax 調用\n        /// <summary>\n        /// 取得角色清單(排除登入者的所屬角色)\n        /// </summary>\n        /// <returns></returns>\n        [MvcAuthFilter(AuthToken.Query)]\n        public JsonResult GetRolesExceptUserRoles()\n        {\n            var res = _RoleService.ListRolesExceptUserRoles(LoginUserTool.UserID).Select(x => new\n            {\n                ID = x.RoleID,\n                x.RoleID,\n                RoleName = x.Name,\n            }).OrderBy(x=> x.RoleName);\n            return Json(res, JsonRequestBehavior.AllowGet);\n        }\n\n        /// <summary>\n        /// 取得樹狀結構\n        /// </summary>\n        /// <returns></returns>\n        [MvcAuthFilter(AuthToken.Query)]\n        public JsonResult LoadTree(int roleId)\n        {\n            //目前角色已設定的權限\n            var roleAuth = _RoleService.ListRoleFunctionTokens(roleId);\n\n            //系統所有功能\n            var rawNodes = MenuTools.AllModules.Traverse(m => m.SubNode);\n\n            //根節點\n            var hasAnySelectedNode = false;\n            var root = new RoleAuthTreeNode<string>\n            {\n                id = MenuTools.RootToken,\n                type = \"root\",\n                text = MenuResource.功能權限樹,\n                state = new ViewModels.JsTreeNodeState { opened = true },\n                data = null,\n                children = EntityToJsTreeNode(0, MenuTools.RootToken, rawNodes, roleAuth, ref hasAnySelectedNode),\n            };\n            root.state.@checked = hasAnySelectedNode;\n\n            var response = new ApiJsonResponse\n            {\n                Status = ApiResponseStatusEnum.Success,\n                JsonData = new JavaScriptSerializer().Serialize(root),\n            };\n\n            return Json(response, JsonRequestBehavior.AllowGet);\n        }\n\n        private List<RoleAuthTreeNode<string>> EntityToJsTreeNode(int? parentID, string token,IEnumerable<PermissionMenu> funcSource, List<string> roleAuth, ref bool hasAnySelectedNode)\n        {\n            var rawNodes = funcSource.Where(x => x.Parent.Token == token && \n                                                (x.FuncType == FuncTypes.Menu || x.FuncType == FuncTypes.Function))\n                                     .OrderBy(x => x.Sort);\n            if (rawNodes.Any())\n            {\n                //子節點資訊\n                var nodes = new List<RoleAuthTreeNode<string>>();\n                foreach (var mode in rawNodes)\n                {\n                    //子節點有事件\n                    var childrenEvent = funcSource.Where(x => x.Parent.Token == mode.Token && x.FuncType == FuncTypes.Event)\n                                                  .OrderBy(x => x.Sort).AsEnumerable()\n                                                  .Select(d => new AuthEventNode\n                                                  {\n                                                      Token = d.Token,\n                                                      ParentToken = mode.Token,\n                                                      Name = ResourcesTools.GetButtonRes(d.Name),\n                                                      FuncType = d.FuncType,\n                                                      IsSelect = roleAuth.Contains(d.Token)\n                                                  }).ToList();\n\n                   //圖示\n                    var imgType = \"folder\";\n                    switch (mode.FuncType)\n                    {\n                        case FuncTypes.Function:\n                            imgType = \"function\";\n                            break;\n                    }\n\n                    //該角色是否已經設定該功能，如果則是checked\n                    var IsSelct = roleAuth.Contains(mode.Token);\n\n                    var childNode = new RoleAuthTreeNode<string>()\n                    {\n                        id = mode.Token,//r.FuncID.ToString(),\n                        type = imgType,\n                        text = ResourcesTools.GetMenuRes(mode.Name),\n                        token = mode.Token,\n                        state = new ViewModels.JsTreeNodeState\n                        {\n                            opened = true,\n                            checkbox_disabled = false,//(!LoginUserTool.HasPermission(AuthCode.SS.BS.CompanyRole.Modify)),\n                            @checked = IsSelct\n                        },\n                        data = childrenEvent,\n                        children = EntityToJsTreeNode(0, mode.Token,funcSource,roleAuth, ref hasAnySelectedNode)\n                    };\n\n                    nodes.Add(childNode);\n                    hasAnySelectedNode = hasAnySelectedNode || IsSelct;\n                }\n\n                return nodes;\n            }\n\n            return new List<RoleAuthTreeNode<string>>(0);\n        }\n        #endregion\n    }\n}\n\n"}, {"text": "--- RoyalBase/Core/MVC/BaseController.cs ---\n\n// 檔案摘要：\n// MVC 控制器的基礎抽象類別，提供所有控制器共用的功能。管理頁面的顯示模式（ViewModes）和操作狀態（ViewActions），支援編輯模式和唯讀模式的切換。提供按鈕顯示控制功能，可根據不同模式自動設定各種操作按鈕的可見性。包含強制模式設定（ChangeForceViewMode）用於特殊權限控制。此基礎類別確保整個系統的控制器行為一致性，簡化了權限和 UI 狀態的管理。\n//\n\nusing System;\nusing RoyalBase.Enum;\nusing System.Web.Mvc;\n\nnamespace Core.MVC\n{\n    /// <summary>\n    /// Controller基底類別(目前無用途)\n    /// </summary>\n    public abstract class BaseController : Controller\n    {\n        protected enum Buttons\n        {\n            DeleteBtn,\n        }\n\n        protected BaseController()\n        {\n            ViewBag.ViewMode = ViewModes.Edit;\n            ViewBag.ViewAction = ViewActions.None;\n        }\n\n        /// <summary>\n        /// 改變頁面顯示模式 (內容有用到再補)\n        /// </summary>\n        /// <param name=\"mode\"></param>\n        protected void ChangeViewMode(ViewModes mode)\n        {\n            ViewBag.ViewMode = mode;\n            switch (mode)\n            {\n                case ViewModes.Edit:\n                    break;\n\n                case ViewModes.Read:\n                    ViewBag.SaveBt = false;\n                    ViewBag.SaveBt_Temp = false;\n                    ViewBag.DeleteBt = false;\n                    ViewBag.ThrowBt = false;\n                    ViewBag.ThrowDistBt = false;\n                    ViewBag.ThrowShipBt = false;\n                    ViewBag.ToPurchaseNoticeBt = false;\n                    ViewBag.FinishBt = false;\n                    ViewBag.ConfirmBt = false;\n                    break;\n                default:\n                    throw new ArgumentOutOfRangeException(nameof(mode), mode, null);\n            }\n        }\n\n        protected void ChangeForceViewMode(ViewModes mode)\n        {\n            ViewBag.ViewMode = mode;\n            switch (mode)\n            {\n                case ViewModes.Edit:\n                    ViewBag.ForcedSaveBt = true;\n                    ViewBag.CanEditAfterThrow = true;\n                    ViewBag.ForcedDeleteBt = true;\n                    break;\n\n                case ViewModes.Read:\n                    ViewBag.ForcedSaveBt = false;\n                    ViewBag.CanEditAfterThrow = false;\n                    ViewBag.ForcedDeleteBt = false;\n                    break;\n                default:\n                    throw new ArgumentOutOfRangeException(nameof(mode), mode, null);\n            }\n        }\n\n        protected void ChangeViewAction(ViewActions act)\n        {\n            ViewBag.ViewAction = act;\n        }\n\n        /// <summary>\n        /// 設定按鈕Visible  (內容有用到再補)\n        /// </summary>\n        /// <param name=\"button\"> ex: ViewBag.DeleteBt</param>\n        /// <param name=\"visible\"></param>\n        protected void SetButtonVisible(Buttons button, bool visible)\n        {\n            switch (button)\n            {\n                case Buttons.DeleteBtn:\n                    ViewBag.DeleteBt = visible;\n                    break;\n                default:\n                    throw new ArgumentOutOfRangeException(nameof(button), button, null);\n            }\n        }\n\n        protected static void SetSaveAndRefreshButton(dynamic viewBag)\n        {\n            viewBag.SaveBt = false;\n            viewBag.SaveAndRefreshBt = true;\n        }\n        protected static void SetSaveAndHistoryBackButton(dynamic viewBag)\n        {\n            viewBag.SaveBt = false;\n            viewBag.SaveAndHistoryBackBt = true;\n        }\n\n    }\n}\n\n"}, {"text": "--- RoyalBase/Filters/MvcAuthFilter.cs ---\n\n// 檔案摘要：\n// MVC 認證授權過濾器，負責處理網頁請求的身分驗證和功能權限檢查。使用 Session 進行身分驗證，支援角色權限檢查。主要功能包括：檢查使用者登入狀態、載入使用者資訊到快取、驗證功能權限代碼、管理員權限優先通過、支援 MvcNoAuthFilter 排除機制。對於 Ajax 請求拋出 401 錯誤，一般請求則導向登入頁或首頁並顯示權限不足訊息。\n//\n\nusing Core.MVC.ViewModels;\nusing RoyalBase.Resources;\nusing RoyalBase.ViewModels;\nusing RoyalBase.Tools;\nusing System.Web;\nusing System.Web.Mvc;\nusing System.Linq;\nusing System;\nusing System.Net;\nusing RoyalBase.Service;\n\nnamespace RoyalBase.Filters\n{\n    /// <summary>MVC認證授權Filter</summary>\n    public class MvcAuthFilter : AuthorizeAttribute\n    {\n        private readonly PersonelService _personelRepo;\n        private readonly LoginService _loginService;\n        /// <summary>功能權限代碼</summary>\n        private string[] PermissionTokens { get; set; }\n\n        public MvcAuthFilter(params string[] permissionTokens)\n        {\n            PermissionTokens = permissionTokens;\n            _personelRepo = new PersonelService();\n            _loginService = new LoginService();\n        }\n\n        /// <summary>\n        /// 自訂權限驗證(如果未設定權限代碼，表示不綁功能權限)\n        /// </summary>\n        /// <param name=\"httpContext\"></param>\n        /// <returns></returns>\n        protected override bool AuthorizeCore(HttpContextBase httpContext)\n        {\n            var authorize = false;\n            // @todo: Session裡存放登入資訊。\n            if (HttpContext.Current.Session[\"SessionID\"] == null)\n            {\n                return false;\n            }\n            if (!LoginUserTool.ExistLoginUser())\n            {\n                if (HttpContext.Current.Session[\"AccountID\"] == null)\n                {\n                    return false;\n                }\n\n                try\n                {\n                    var accountID = (int)HttpContext.Current.Session[\"AccountID\"];\n                    var person = _personelRepo.Get(accountID);\n                    var account = _loginService.GetLoginUser(person);\n                    LoginUserTool.SetLoginUser(account);\n                }\n                catch (Exception e)\n                {\n                    return false;\n                }\n            }\n\n            if (LoginUserTool.IsAdmin)\n            {\n                return true;\n            }\n            //檢查角色權限\n            if (PermissionTokens.Any(p => p == \"IsAdmin\"))      //todo:IsAdmin作用待查\n            {\n                authorize = LoginUserTool.IsAdmin;\n            }\n            else\n            {\n                authorize = (PermissionTokens == null || !PermissionTokens.Any())\n                ? LoginUserTool.IsAuthenticated\n                : LoginUserTool.HasPermission(PermissionTokens);\n            }\n            return authorize;\n        }\n\n        /// <summary>\n        /// 在處理序要求授權時呼叫\n        /// </summary>\n        /// <param name=\"filterContext\"></param>\n        public override void OnAuthorization(AuthorizationContext filterContext)\n        {\n            // 判斷有冠上NoAuthFilter，並記錄在noAuthFilter私有變數。\n            var hasToBeAuth = !(filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(MvcNoAuthFilter), true) || filterContext.ActionDescriptor.IsDefined(typeof(MvcNoAuthFilter), true));\n\n            var sessionID = HttpContext.Current.Session[\"SessionID\"];\n            if (sessionID != null)\n            {\n                filterContext.Controller.ViewBag.Account = LoginUserTool.GetLoginUser();\n            }\n\n            if (hasToBeAuth)\n            {\n                base.OnAuthorization(filterContext);\n            }\n        }\n        /// <summary>\n        /// 處理未通過驗證的狀況\n        /// </summary>\n        /// <param name=\"filterContext\"></param>\n        protected override void HandleUnauthorizedRequest(AuthorizationContext filterContext)\n        {\n            ActionResult result = new ContentResult();\n\n            // Ajax post case.\n            if (filterContext.RequestContext.HttpContext.Request.IsAjaxRequest())\n            {\n                throw new HttpException((int)HttpStatusCode.Unauthorized, MessageResource.無功能權限);\n            }\n\n            // 非Ajax post, 也就是MVC case.\n            var controller = (Controller)filterContext.Controller;\n\n            // 沒該功能權限，改回到首頁\n            var sessionID = HttpContext.Current.Session[\"SessionID\"];\n            if (sessionID != null && LoginUserTool.ExistLoginUser() && LoginUserTool.IsAuthenticated)\n            {\n                ((ContentResult)result).Content = \"<script>alert('\" + MessageResource.無功能權限 + \"');window.location.href ='\" + controller.Url.Action(\"Index\", \"Home\") + \"'</script>\";\n            }\n            else\n            {\n                // 沒有登入，回到登入頁\n                ((ContentResult)result).Content = \"<script>window.location.href ='\" + controller.Url.Action(\"Login\", \"Home\", new { ReturnURL = $\"{filterContext.HttpContext.Request.Url.PathAndQuery}\" }) + \"'</script>\";\n            }\n            filterContext.Result = result;\n        }\n    }\n}\n\n"}, {"text": "--- RoyalBase/Filters/MvcButtonFilter.cs ---\n\n// 檔案摘要：\n// MVC 按鈕權限過濾器，負責控制頁面工具列按鈕的顯示權限。根據使用者角色權限和頁面狀態動態決定各種操作按鈕的可見性。支援多種按鈕類型：編輯（新增/儲存/複製）、刪除、載入、拋轉、郵件、結束（結案/確認配貨）、列印等。針對不同的控制器和動作方法提供客製化按鈕配置，確保使用者只能看到有權限執行的操作按鈕。\n//\n\nusing RoyalBase.Resources;\nusing RoyalBase.Service;\nusing RoyalBase.Tools;\nusing RoyalBase.ViewModels;\nusing System.Collections.Generic;\nusing System.Web.Mvc;\n\nnamespace RoyalBase.Filters\n{\n    /// <summary>按鈕ActionFilter</summary>\n    public class MvcButtonFilter : ActionFilterAttribute\n    {\n        public enum PermissionButtonType\n        {\n            /// <summary>\n            /// 編輯：新增/新增驗貨單/儲存/暫存/複製\n            /// </summary>\n            Edit = 1,\n            /// <summary>\n            /// 刪除\n            /// </summary>\n            Delete = 2,\n            /// <summary>\n            /// 載入/載入驗貨單\n            /// </summary>\n            Load = 4,\n            /// <summary>\n            /// 拋轉/拋轉ERP/轉現場出貨\n            /// </summary>\n            Throw = 8,\n            /// <summary>\n            /// 寄送/收件\n            /// </summary>\n            Mail = 16,\n            /// <summary>\n            /// 結束：結案/結束安排/確認並配貨\n            /// </summary>\n            Finish = 32,\n            /// <summary>\n            /// 列印\n            /// </summary>\n            Print = 64,\n            /// <summary>\n            /// 編輯/刪除\n            /// </summary>\n            EditDelete = Edit | Delete,\n            /// <summary>\n            /// 新增/載入\n            /// </summary>\n            EditLoad = Edit | Load,\n            /// <summary>\n            /// 編輯/結束(結案,結束安排,確認並配貨)\n            /// </summary>\n            EditFinish = Edit | Finish,\n            /// <summary>\n            /// 編輯/刪除/拋轉\n            /// </summary>\n            EditDeleteThrow = EditDelete | Throw,\n            /// <summary>\n            /// 編輯/拋轉\n            /// </summary>\n            EditThrow = Edit | Throw,\n            /// <summary>\n            /// 編輯/刪除/郵件(寄送/收件)\n            /// </summary>\n            EditDeleteMail = EditDelete | Mail,\n        }\n\n        private TransferTaskService transferTaskRepo;      // TODO 智維：MvcButtonFilter職責不應牽扯資料庫使用，待優化\n\n        /// <summary>功能權限</summary>\n        private Dictionary<PermissionButtonType, string> PermissionButtons { get; set; } = new Dictionary<PermissionButtonType, string>();\n\n        #region Constructor\n        public MvcButtonFilter()\n        {\n\n        }\n\n        public MvcButtonFilter(PermissionButtonType btnType, params string[] tokens)\n        {\n            if (tokens == null || tokens.Length == 0) return;\n\n            switch (btnType)\n            {\n                case PermissionButtonType.EditDelete:\n                    {\n                        PermissionButtons.Add(PermissionButtonType.Edit, tokens[0]);\n                        if (tokens.Length > 1)\n                            PermissionButtons.Add(PermissionButtonType.Delete, tokens[1]);\n                        break;\n                    }\n                case PermissionButtonType.EditLoad:   //進貨任務管理\n                    {\n                        PermissionButtons.Add(PermissionButtonType.Edit, tokens[0]);\n                        if (tokens.Length > 1)\n                            PermissionButtons.Add(PermissionButtonType.Load, tokens[1]);\n                        break;\n                    }\n                case PermissionButtonType.EditFinish: //YP配貨作業管理、挑苗排程管理\n                    {\n                        PermissionButtons.Add(PermissionButtonType.Edit, tokens[0]);\n                        if (tokens.Length > 1)\n                            PermissionButtons.Add(PermissionButtonType.Finish, tokens[1]);\n                        break;\n                    }\n                case PermissionButtonType.EditThrow:\n                    {\n                        PermissionButtons.Add(PermissionButtonType.Edit, tokens[0]);\n                        if (tokens.Length > 1)\n                            PermissionButtons.Add(PermissionButtonType.Throw, tokens[1]);\n                        break;\n                    }\n                case PermissionButtonType.EditDeleteThrow:\n                    {\n                        //PermissionButtons.Add(PermissionButtonType.Edit, tokens[0]);\n                        //if (tokens.Length > 1)\n                        //    PermissionButtons.Add(PermissionButtonType.Delete, tokens[1]);\n                        if (tokens.Length > 2)\n                            PermissionButtons.Add(PermissionButtonType.Throw, tokens[2]);\n                        goto case PermissionButtonType.EditDelete;\n                    }\n                case PermissionButtonType.EditDeleteMail:\n                    {\n                        //PermissionButtons.Add(PermissionButtonType.Edit, tokens[0]);\n                        //if (tokens.Length > 1)\n                        //    PermissionButtons.Add(PermissionButtonType.Delete, tokens[1]);\n                        if (tokens.Length > 2)\n                            PermissionButtons.Add(PermissionButtonType.Mail, tokens[2]);\n                        goto case PermissionButtonType.EditDelete;\n                    }\n                default:\n                    PermissionButtons.Add(btnType, tokens[0]);\n                    break;\n            }\n        }\n\n        #endregion\n\n        /// <summary>ActionResult 進入前</summary>\n        /// <param name=\"filterContext\"></param>\n        public override void OnActionExecuting(ActionExecutingContext filterContext)\n        {\n            base.OnActionExecuting(filterContext);\n            var hasToBeFilter = !(filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(MvcNoButtonFilter), true) || filterContext.ActionDescriptor.IsDefined(typeof(MvcNoButtonFilter), true));\n            if (!hasToBeFilter)\n            {\n                return;\n            }\n\n            // 取得Controller與Action Name\n            var controller = filterContext.RouteData.Values[\"controller\"].ToString();\n            var action = filterContext.RouteData.Values[\"action\"].ToString();\n\n            // 與頁面無關的Action不處理按鈕功能\n            if (action != \"Index\" && action != \"Create\" && action != \"Modify\" && action != \"Transfer\" && action != \"Distribution\")\n            {\n                return;\n            }\n\n            // 定義按鈕，預設值不顯示\n            filterContext.Controller.ViewBag.CreateBt = false;          // 新增\n            filterContext.Controller.ViewBag.CreateBtName = ButtonResource.新增;\n            filterContext.Controller.ViewBag.CreateBt_Lightbox = false; // 新增(燈箱用)\n            filterContext.Controller.ViewBag.LoadBt = false;            // 載入(目前苗株進貨任務用)\n            filterContext.Controller.ViewBag.LoadBtName = ButtonResource.載入;\n            filterContext.Controller.ViewBag.CancelBt = false;          // 取消\n            filterContext.Controller.ViewBag.DeleteBt = false;          // 刪除\n            filterContext.Controller.ViewBag.SaveBt = false;            // 儲存\n            filterContext.Controller.ViewBag.ForcedSaveBt = false;     // 強制儲存(移轉紀錄)\n            filterContext.Controller.ViewBag.ForcedSaveBtName = ButtonResource.儲存;\n            filterContext.Controller.ViewBag.CopyBt = false;            // 複製\n            filterContext.Controller.ViewBag.SaveBt_Temp = false;       // 暫存\n            filterContext.Controller.ViewBag.ApproveBt = false;         // 簽核 (自訂)\n            filterContext.Controller.ViewBag.RejectBt = false;          // 簽退 (自訂)\n            filterContext.Controller.ViewBag.FinishBt = false;          // 結束安排\n            filterContext.Controller.ViewBag.ConfirmBt = false;         // 確認並配貨\n            filterContext.Controller.ViewBag.BatchBt = false;           // 批次設定\n            filterContext.Controller.ViewBag.ReturnBt = false;          // 退回\n            filterContext.Controller.ViewBag.ThrowBt = false;           // 拋轉\n            filterContext.Controller.ViewBag.ThrowBtnName = ButtonResource.拋轉;\n            filterContext.Controller.ViewBag.ThrowDistBt = false;       // 轉配貨\n            filterContext.Controller.ViewBag.ThrowShipBt = false;       // 轉現場出貨\n            filterContext.Controller.ViewBag.QueryBtn = false;           // 查詢\n            filterContext.Controller.ViewBag.QueryBtnName = ButtonResource.查詢;\n            filterContext.Controller.ViewBag.PrintBtn = false;           // 列印\n            filterContext.Controller.ViewBag.PrintBtnName = ButtonResource.列印;\n            //太爛了,一堆按鈕, 改用自訂按鈕, 各自JS寫event\n            filterContext.Controller.ViewBag.CustomBtn1 = false;           // 自訂按鈕1\n            filterContext.Controller.ViewBag.CustomerBtn1Name = \"undefined\";\n\n\n            // 預設工具列按鈕(依Action Name配置預設按鈕)\n            switch (action)\n            {\n                case \"Index\":\n                    filterContext.Controller.ViewBag.CreateBt = true;\n                    break;\n                case \"Create\":\n                case \"Modify\":\n                case \"Transfer\":\n                    filterContext.Controller.ViewBag.SaveBt = true;\n                    filterContext.Controller.ViewBag.CancelBt = true;\n                    break;\n                case \"Distribution\":\n                    filterContext.Controller.ViewBag.CreateBt = true;\n                    filterContext.Controller.ViewBag.ConfirmBt = true;\n                    filterContext.Controller.ViewBag.CancelBt = true;\n                    break;\n                default:\n                    break;\n            }\n\n            // 依頁面客製化按鈕\n            switch (controller)\n            {\n                //燈箱式維護畫面\n                case \"LightBoxMode\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        break;\n                    }\n                case \"BICustomer\":\n                case \"BICompany\":  // 新增公司資料管理\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        if (action.Equals(\"Index\"))\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = true;\n                        }\n                        break;\n                    }\n                //沒有新增功能\n                case \"BISupplier\":\n                case \"BISystemOption\":\n                case \"YPPickingAllocation\":\n                case \"YPShipPlan\":\n                    filterContext.Controller.ViewBag.CreateBt = false;\n                    break;\n                case \"YPPickingPlanAllocation\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n\n                        // 挑苗排程配貨新增/編輯頁有【暫存】、【確認並配貨】，沒有【儲存】\n                        if (action.Equals(\"Create\") || action.Equals(\"Modify\"))\n                        {\n                            filterContext.Controller.ViewBag.SaveBt = false;\n                            filterContext.Controller.ViewBag.SaveBt_Temp = true;\n                            filterContext.Controller.ViewBag.ConfirmBt = true;\n                        }\n                        break;\n                    }\n                //樹狀式維護畫面\n                case \"BSRole\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.SaveBt = true;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        break;\n                    }\n                case \"BIReason\":\n                    filterContext.Controller.ViewBag.CreateBt = false;\n                    filterContext.Controller.ViewBag.SaveBt = true;\n                    break;\n                //燈箱式維護畫面\n                case \"BISpec\":\n                case \"BIColorCategory\":\n                case \"BIPot\":\n                case \"BIMedium\":\n                case \"BIProcess\":\n                case \"BILevelMapping\":\n                case \"BICutFlowerMapping\":\n                case \"BIBreedMapping\":\n                case \"BIDefaultPacking\":\n                case \"YPTransferTask\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        break;\n                    }\n                case \"BIColor\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n\n                        if (action.Equals(\"Index\"))\n                        {\n                            filterContext.Controller.ViewBag.BatchBt = true;\n                        }\n                        break;\n                    }\n                case \"YPTransferSummary\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        break;\n                    }\n                case \"YPReturnTask\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n\n                        if (action.Equals(\"Create\") || action.Equals(\"Modify\"))\n                        {\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                        }\n                        break;\n                    }\n                case \"CFTransferRecord\":\n                case \"YPTransferRecord\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n\n                        if (action.Equals(\"Index\") || action.Equals(\"Modify\"))\n                        {\n                            filterContext.Controller.ViewBag.DeleteBt = true;\n                        }\n                        if (action.Equals(\"Create\") || action.Equals(\"Modify\"))\n                        {\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                        }\n                        break;\n                    }\n                case \"YPRepot\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n\n                        if (action.Equals(\"Create\") || action.Equals(\"Modify\"))\n                        {\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                        }\n                        else\n                        {\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        }\n                        break;\n                    }\n                case \"YPCrossYPtoPP\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = action.Equals(\"Index\");\n                        break;\n                    }\n                case \"BIPersonel\":\n                    {\n                        if (action.Equals(\"Index\"))\n                            filterContext.Controller.ViewBag.BatchBt = true;\n                        break;\n                    }\n                case \"PPTransferRecord\":\n                case \"PPCrossYPtoPP\":\n                    {\n                        if (action == \"Index\")\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                            filterContext.Controller.ViewBag.DeleteBt = true;\n                        }\n                        else if (action == \"Create\")\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                        }\n                        else if (action == \"Modify\")\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                            filterContext.Controller.ViewBag.DeleteBt = false;\n                        }\n                        break;\n                    }\n                case \"CFOrder\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        if (action == \"Modify\")\n                        {\n                            filterContext.Controller.ViewBag.CopyBt = true;\n                        }\n                        break;\n                    }\n                case \"PPOrder\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        if (action == \"Modify\")\n                        {\n                            filterContext.Controller.ViewBag.CopyBt = true;\n                        }\n                        break;\n                    }\n                case \"PPOrderConfirm\":\n                    {\n                        if (action == \"Index\")\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                        }\n                        else if (action == \"Modify\")\n                        {\n                            filterContext.Controller.ViewBag.ThrowShipBt = true;\n                        }\n                        break;\n                    }\n                case \"BIPackingColor\":\n                case \"BIPackingSize\":\n                case \"CFDistribution\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        if (action == \"Index\")\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        }\n                        else if (action == \"Modify\")\n                        {\n                            filterContext.Controller.ViewBag.ThrowBt = true;\n                        }\n                        break;\n                    }\n                case \"PRCVarietyCostType\":\n                    {\n                        if (action.Equals(\"Index\"))\n                        {\n                            filterContext.Controller.ViewBag.BatchBt = true;\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                        }\n                        break;\n                    }\n                case \"PRCProductCostManagement\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n\n                        if (action.Equals(\"Index\"))\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        }\n                        if (action.Equals(\"Modify\"))\n                        {\n                            filterContext.Controller.ViewBag.CopyBt = true;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                        }\n                        break;\n                    }\n                case \"PPDistribution\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        if (action == \"Index\")\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = false;\n                            filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        }\n                        else if (action == \"Modify\")\n                        {\n                            filterContext.Controller.ViewBag.ThrowBt = false;\n                        }\n                        break;\n                    }\n                case \"YPProductionBatch\":\n                    {\n                        if (action.Equals(\"Index\"))\n                            filterContext.Controller.ViewBag.BatchBt = true;\n                        break;\n                    }\n                case \"BIPPPackingCondition\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        break;\n                    }\n                case \"BIShipPacking\":\n                case \"CFSampleSurvey\":\n                case \"PPStemSurvey\":\n                case \"PPBudSurvey\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        break;\n                    }\n                case \"CFStemSurvey\":\n                case \"CFShipSurvey\":\n                    filterContext.Controller.ViewBag.UpdateYieldBt = true;\n                    filterContext.Controller.ViewBag.DeleteBt = true;\n                    break;\n                case \"SCPurchaseOrder\":\n                    {\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        break;\n                    }\n                case \"YPPurchase\":\n                    {\n                        //進貨功能新增按鈕名稱改為新增驗貨單\n                        if (action.Equals(\"Index\"))\n                        {\n                            filterContext.Controller.ViewBag.CreateBt = true;\n                            filterContext.Controller.ViewBag.CreateBtName = ButtonResource.新增驗貨單;\n                            filterContext.Controller.ViewBag.LoadBt = true;\n                            filterContext.Controller.ViewBag.LoadBtName = ButtonResource.載入驗貨單;\n                        }\n                        break;\n                    }\n                case \"BISpecMapping\":\n                case \"BIMediumMapping\":\n                case \"BIProductMapping\":\n                case \"BICurrencyRate\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.DeleteBt = false;\n                        filterContext.Controller.ViewBag.SaveBt = true;\n                        break;\n                    }\n                case \"Log\":\n                case \"log\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        break;\n                    }\n                case \"CF001\":\n                case \"CF002\":\n                case \"CF003\":\n                case \"CF004\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.PrintBtn = true;\n                        break;\n                    }\n                case \"BIB2BCustomerMap\":\n                    {\n                        filterContext.Controller.ViewBag.CreateBt = false;\n                        filterContext.Controller.ViewBag.CreateBt_Lightbox = true;\n                        filterContext.Controller.ViewBag.DeleteBt = true;\n                        break;\n                    }\n                default:\n                    break;\n            }\n\n            if (action == \"Create\") //為了省去判斷, 新增頁直接關閉刪除按紐\n                filterContext.Controller.ViewBag.DeleteBt = false;\n\n            // 個別客製化\n            // 挑苗排程新增/編輯頁有【暫存】、【結束安排】，沒有【儲存】\n            if (controller.Equals(\"YPPickingPlan\"))\n            {\n                filterContext.Controller.ViewBag.DeleteBt = true;\n                if (action.Equals(\"Create\") || action.Equals(\"Modify\"))\n                {\n                    filterContext.Controller.ViewBag.SaveBt = false;\n                    filterContext.Controller.ViewBag.SaveBt_Temp = true;\n                    filterContext.Controller.ViewBag.FinishBt = true;\n                }\n            }\n        }\n\n        public override void OnActionExecuted(ActionExecutedContext filterContext)\n        {\n            base.OnActionExecuted(filterContext);\n            var hasToBeFilter = !(filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(MvcNoButtonFilter), true) || filterContext.ActionDescriptor.IsDefined(typeof(MvcNoButtonFilter), true));\n            if (!hasToBeFilter)\n            {\n                return;\n            }\n\n            var Controller = filterContext.RouteData.Values[\"controller\"].ToString();\n            var Action = filterContext.RouteData.Values[\"action\"].ToString();\n\n            if (Controller.Equals(\"YPTransferTask\") && (Action.Equals(\"Create\") || Action.Equals(\"Modify\")))\n            {\n                filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                if (Action.Equals(\"Modify\") && filterContext.Result is ViewResult viewResult)\n                {\n                    transferTaskRepo = new TransferTaskService();\n                    var model = viewResult.Model as TransferTaskModify;\n                    var IsExistReturnTask = transferTaskRepo.IsExistReturnTask(model.ID);\n                    var IsComplete = transferTaskRepo.IsComplete(model.ID);\n                    var IsReturnLine = transferTaskRepo.IsReturnLine(model.ID);\n\n                    // 移轉紀錄已完成(刷出數=刷入數) 或 已存在退回任務 時，不可顯示退回、存檔按鈕\n                    bool showReturn = IsReturnLine && !(IsComplete || IsExistReturnTask);\n                    filterContext.Controller.ViewBag.ReturnBt = showReturn;\n                    filterContext.Controller.ViewBag.SaveBt = !(IsComplete || IsExistReturnTask);\n                    if (showReturn)\n                        RegisterCustomBtn(filterContext.Controller, ButtonResource.直接退回);  //#29842\n                }\n            }\n\n            _checkPermissionButtonVisible(filterContext);\n        }\n\n        private void _checkPermissionButtonVisible(ActionExecutedContext filterContext)\n        {\n            if (LoginUserTool.IsAdmin)\n            {\n                return;\n            }\n            var userPermissionList = LoginUserTool.PermissionTokens;\n            foreach (var btn in PermissionButtons)\n            {\n                if (!userPermissionList.Contains(btn.Value))\n                {\n                    switch (btn.Key)\n                    {\n                        case PermissionButtonType.Edit:\n                            {\n                                filterContext.Controller.ViewBag.CreateBt = false;\n                                filterContext.Controller.ViewBag.CreateBt_Lightbox = false;\n                                filterContext.Controller.ViewBag.CopyBt = false;\n                                filterContext.Controller.ViewBag.SaveBt = false;\n                                filterContext.Controller.ViewBag.SaveAndRefreshBt = false;\n                                filterContext.Controller.ViewBag.SaveAndHistoryBackBt = false;\n                                filterContext.Controller.ViewBag.SaveBt_Temp = false;\n                                filterContext.Controller.ViewBag.BatchBt = false;\n                                break;\n                            }\n                        case PermissionButtonType.Load:\n                            {\n                                filterContext.Controller.ViewBag.LoadBt = false;\n                                break;\n                            }\n                        case PermissionButtonType.Delete:\n                            {\n                                filterContext.Controller.ViewBag.DeleteBt = false;\n                                break;\n                            }\n                        case PermissionButtonType.Throw:\n                            {\n                                filterContext.Controller.ViewBag.ThrowBt = false;\n                                filterContext.Controller.ViewBag.ThrowDistBt = false;\n                                filterContext.Controller.ViewBag.ThrowShipBt = false;\n                                break;\n                            }\n                        case PermissionButtonType.Finish:\n                            {\n                                filterContext.Controller.ViewBag.FinishBt = false;\n                                filterContext.Controller.ViewBag.ConfirmBt = false;\n                                break;\n                            }\n                        case PermissionButtonType.Mail:\n                            {\n                                filterContext.Controller.ViewBag.SendMailBt = false;\n                                filterContext.Controller.ViewBag.ToPurchaseNoticeBt = false;\n                                break;\n                            }\n                        case PermissionButtonType.Print:\n                            {\n                                filterContext.Controller.ViewBag.QueryBtn = false;\n                                filterContext.Controller.ViewBag.PrintBtn = false;\n                                break;\n                            }\n                    }\n                }\n            }\n        }\n\n        /// <summary>\n        /// 註冊自訂按鈕\n        /// </summary>\n        /// <param name=\"controller\"></param>\n        /// <param name=\"btn1Name\">自訂按鈕一名稱</param>\n        public static void RegisterCustomBtn(ControllerBase controller, string btn1Name)\n        {\n            //todo: 理論上 樣式和icon都要可設定, 等出現其他要共用再說\n            controller.ViewBag.CustomBtn1 = true;\n            controller.ViewBag.CustomBtn1Name = btn1Name;\n        }\n    }\n}\n\n"}, {"text": "--- RoyalBase/Service/Common/ServiceBase.cs ---\n\n// 檔案摘要：\n// 服務層基礎類別，提供所有Service的共用功能與Repository初始化機制。\n// 繼承自UnitOfWorkContainer，提供Unit of Work模式實作。預先初始化常用的Repository，包含：\n// 基礎資料（BIReason、BIColor、BIBreed、BICustomer、BISystemOption、BIProductionLine、BIPersonel等）、\n// 系統管理（BSRole）、視圖（View_BreedAlias、View_ShipPacking）等。\n// 提供統一的錯誤處理機制，包含ThrowException、ThrowNoData、CreateBusinessLogicException等方法。\n// 支援資料驗證服務（ValidateService）的延遲載入。提供SQL刪除功能（DeleteDatas）與日誌記錄功能。\n// 定義NullDefaultValue常數處理空值顯示。所有繼承的Service都可直接使用預先初始化的Repository，\n// 避免重複宣告，提升開發效率。支援依賴注入模式，可接受外部傳入的IUnitOfWork實例。\n//\n\nusing Core.EF;\nusing Core.Exceptions;\nusing RoyalBase.Models;\nusing RoyalBase.Resources;\nusing RoyalBase.Tools;\nusing System;\nusing System.Collections.Generic;\n\nnamespace RoyalBase.Service\n{\n    public interface IUnitOfWorkContainer\n    {\n        IUnitOfWork UOW  { get; }\n    }\n\n    public abstract class UnitOfWorkContainer : IUnitOfWorkContainer\n    {\n        public IUnitOfWork UOW { get; private set; }\n        public static Lazy<ValidateService> ValidateService =>  new Lazy<ValidateService>(() => new ValidateService());\n\n        protected virtual void Init(IUnitOfWork uow)\n        {\n            UOW = uow ?? new RoyalBaseEFUnitOfWork();\n        }\n\n        /// <summary>\n        /// 拋錯(斷行顯示)\n        /// </summary>\n        /// <param name=\"messages\"></param>\n        /// <param name=\"isNewline\">是否換行</param>\n        protected static void ThrowException(IEnumerable<string> messages, bool isNewline = false)\n        {\n            string separator = isNewline ? \"<br />\" : \",\";\n            ThrowException(string.Join(separator, messages));\n        }\n\n        protected static Exception CreateBusinessLogicException(string message, params object[] parameters)\n        {\n            message = string.Format(message, parameters);\n            return new BusinessLogic(message);\n        }\n\n        protected static void ThrowException(string message, params object[] parameters)\n        {\n            message = string.Format(message, parameters);\n            throw new BusinessLogic(message);\n        }\n\n        protected static void ThrowNoData(string message, params object[] parameters)\n        {\n            message = string.Format(message, parameters);\n            throw new NullReferenceException(message);\n        }\n\n        protected void ThrowNoData(string tableName, int id)\n        {\n            ThrowNoData(MessageResource.資料表_資料不存在_ID, tableName, id);\n        }\n\n        protected static void Log(string message)\n        {\n            BaseLog.Instance.WriteTraceLog(message);\n        }\n    }\n    public abstract class ServiceBase : UnitOfWorkContainer\n    {\n        protected IRepository<BIReason> ReasonRepo { get; private set; }\n        protected IRepository<BIReasonDesc> ReasonDescRepo { get; private set; }\n        protected IRepository<BIColor> ColorRepo { get; private set; }\n        protected IRepository<BIColorCategory> ColorCategoryRepo { get; private set; }\n        protected IRepository<BIBreed> BreedRepo { get; private set; }\n        protected IRepository<BIBreedAlias> BreedAliasRepo { get; private set; }\n        protected IRepository<BICustomer> CustomerRepo { get; private set; }\n        protected IRepository<BISystemOption> SystemOptionRepo { get; private set; }\n        protected IRepository<BIProductionLine> ProductionLineRepo { get; private set; }\n        protected IRepository<BIPersonel> PersonelRepo { get; private set; }\n        protected IRepository<BSRole> RoleRepo { get; private set; }\n        protected IRepository<BISpec> SpecRepo { get; private set; }\n        protected IRepository<BIPosition> PositionRepo { get; private set; }\n        protected IRepository<BIMedium> MediumRepo { get; private set; }\n        protected IRepository<BISupplier> SupplierRepo { get; private set; }\n        protected IRepository<View_BreedAlias> ViewBreedAliasRepo { get; private set; }\n        protected IRepository<View_ShipPacking> ViewShipPackingRepo { get; private set; }\n\n        /// <summary>\n        /// Null的預設值\n        /// </summary>\n        protected static string NullDefaultValue => \"\";\n\n        /// <summary>\n        /// 初始化連線設定\n        /// </summary>\n        /// <param name=\"uow\"></param>\n        protected override void Init(IUnitOfWork uow)\n        {\n            base.Init(uow);\n            ReasonRepo = UOW.Repository<BIReason>();\n            ReasonDescRepo = UOW.Repository<BIReasonDesc>();\n            ColorRepo = UOW.Repository<BIColor>();\n            ColorCategoryRepo = UOW.Repository<BIColorCategory>();\n            BreedRepo = UOW.Repository<BIBreed>();\n            BreedAliasRepo = UOW.Repository<BIBreedAlias>();\n            CustomerRepo = UOW.Repository<BICustomer>();\n            SystemOptionRepo = UOW.Repository<BISystemOption>();\n            ProductionLineRepo = UOW.Repository<BIProductionLine>();\n            PersonelRepo = UOW.Repository<BIPersonel>();\n            RoleRepo = UOW.Repository<BSRole>();\n            SpecRepo = UOW.Repository<BISpec>();\n            PositionRepo = UOW.Repository<BIPosition>();\n            MediumRepo = UOW.Repository<BIMedium>();\n            SupplierRepo = UOW.Repository<BISupplier>();\n            ViewBreedAliasRepo = UOW.Repository<View_BreedAlias>();\n            ViewShipPackingRepo = UOW.Repository<View_ShipPacking>();\n        }\n\n        /// <summary>\n        /// 使用SQL刪除資料\n        /// </summary>\n        /// <param name=\"tableName\">資料表名稱</param>\n        /// <param name=\"pkField\">主鍵值欄位</param>\n        /// <param name=\"deleteIDs\">刪除的值集合</param>\n        protected void DeleteDatas(string tableName, string pkField, IEnumerable<int> deleteIDs)\n        {\n            var sql = $@\"Delete {tableName} WHERE {pkField} in ({string.Join(\",\", deleteIDs)})\";\n            UOW.GetDbContext().Database.ExecuteSqlCommand(sql);\n            BaseLog.Instance.WriteTraceLog(\"資料刪除：\" + sql);\n        }\n\n    }\n}\n\n"}]}, {"role": "model", "parts": [{"text": "好的，我已經閱讀了您提供的所有核心檔案。現在，我將根據這些資訊，向您報告我的第一階段分析結果。\n\n### **第一階段分析報告**\n\n#### **1. 權限檢查核心 (`MvcAuthFilter` & `MvcButtonFilter`)**\n\n*   **`MvcAuthFilter.cs`**:\n    *   **權限來源**: 權限檢查的核心是 `LoginUserTool.HasPermission(PermissionTokens)`。這個 `LoginUserTool` 是一個靜態工具類，它從 `HttpContext.Current.Session` 中讀取和快取登入使用者的資訊，包括他們的權限 `PermissionTokens`。\n    *   **驗證流程**:\n        1.  `AuthorizeCore` 方法首先檢查 `Session[\"SessionID\"]` 是否存在，如果不存在，直接拒絕存取。\n        2.  如果 Session 存在，但 `LoginUserTool` 中沒有使用者資訊，它會嘗試從 Session 取得 `AccountID`，然後呼叫 `_loginService.GetLoginUser(person)` 重新從資料庫取得使用者資料，並用 `LoginUserTool.SetLoginUser(account)` 將其存入 `LoginUserTool` 的靜態變數中（這些資料最終可能還是來自 Session）。\n        3.  最後，呼叫 `LoginUserTool.HasPermission()` 來比對使用者擁有的權限和當前操作所需的權限。\n    *   **關鍵發現**: **權限資料被明確地儲存在伺服器的 Session 中**。`MvcAuthFilter` 完全依賴 `LoginUserTool`，而 `LoginUserTool` 依賴 Session。這證實了我的初步假設。\n\n*   **`MvcButtonFilter.cs`**:\n    *   **權限來源**: 與 `MvcAuthFilter` 類似，它在 `_checkPermissionButtonVisible` 方法中也呼叫了 `LoginUserTool.PermissionTokens` 來獲取使用者擁有的權限列表。\n    *   **驗證流程**: 它根據 Controller 和 Action 的名稱，預先設定好一組按鈕的顯示狀態 (`ViewBag.CreateBt = true` 等)，然後在 `OnActionExecuted` 事件中，根據 `LoginUserTool` 提供的權限列表，將沒有權限的按鈕的 `ViewBag` 屬性設為 `false`。\n    *   **關鍵發現**: 按鈕的顯示權限同樣**強烈依賴儲存在 Session 中的權限資料**。\n\n#### **2. 權限載入時機 (Login)**\n\n*   **尋找登入邏輯**: 雖然沒有找到 `AccountController` 或 `LoginController`，但在 `MvcAuthFilter` 的 `HandleUnauthorizedRequest` 方法中，我發現了這段程式碼：\n    ```csharp\n    // 沒有登入，回到登入頁\n    ((ContentResult)result).Content = \"<script>window.location.href ='\" + controller.Url.Action(\"Login\", \"Home\", new { ReturnURL = $\"{filterContext.HttpContext.Request.Url.PathAndQuery}\" }) + \"'</script>\";\n    ```\n*   **關鍵發現**: 這段程式碼清楚地指出，登入頁面是由 `HomeController` 的 `Login` Action 處理的。因此，權限的初始載入點應該就在 `HomeController.cs` 的 `Login` 方法中。我需要進一步閱讀這個檔案來確認權限是如何被查詢並存入 Session 的。\n\n#### **3. 權限儲存邏輯 (`BSRoleController`)**\n\n*   **`BSRoleController.cs`**:\n    *   **儲存流程**: 當管理員在頁面上修改權限並儲存時，前端會呼叫 `SavePermission` Action。這個 Action 接著會呼叫 `_RoleService.SavePermission(roleId, tokens)`。\n    *   **`RoleService`**: `BSRoleController` 將儲存權限的具體邏輯委託給了 `RoleService`。因此，`RoleService` 負責將新的權限設定寫入資料庫。\n    *   **關鍵發現**: `BSRoleController` 在儲存權限後，**完全沒有執行任何清除或更新現有使用者 Session 的操作**。它僅僅是更新了資料庫，然後回傳一個「儲存成功」的訊息。這就是為什麼已登入的使用者權限沒有被更新的根本原因。\n\n#### **4. 基底類別 (`BaseController`, `ServiceBase`)**\n\n*   **`BaseController.cs`**: 主要處理 `ViewBag` 的狀態（如頁面模式、按鈕可見性），與權限資料的來源沒有直接關係。\n*   **`ServiceBase.cs`**: 提供了 `UnitOfWork` 和共享的 `Repository` 實例，是資料庫操作的基礎。它本身不包含任何與使用者 Session 或權限快取相關的邏輯。\n\n### **第一階段結論**\n\n綜合以上分析，我已經清晰地描繪出現有系統的權限運作模式：\n\n1.  **登入時**: `HomeController` 的 `Login` Action 會呼叫某個 Service (很可能是 `LoginService` 或 `RoleService`)，從資料庫取得使用者的角色與對應的所有功能權限 (Permission Tokens)，然後將這些權限資料完整地存入 `Session`。\n2.  **請求時**: `MvcAuthFilter` 和 `MvcButtonFilter` 在每次請求時，都會從 `Session` 中讀取這些預先存好的權限資料來進行驗證，而不會再次查詢資料庫。\n3.  **更新時**: `BSRoleController` 在更新角色權限時，只修改了資料庫，完全沒有通知或更新任何已登入使用者的 `Session`。\n\n這個架構導致了目前的問題：**Session 中的權限資料變成了「過期資料」**，直到使用者登出（銷毀 Session）並重新登入（建立新的 Session 並重新讀取資料庫）時，才會被更新。\n\n---\n\n現在，第一階段的分析已經完成。我已經對系統的運作方式有了透徹的理解。\n\n接下來，我將根據這些發現，進入**第二階段**，為您設計具體的解決方案。我會提出至少兩種方案，並詳細解釋它們的實作步驟、優點和缺點，以便您做出決策。\n\n請問您是否需要我立即開始第二階段，為您提出解決方案？"}]}, {"role": "user", "parts": [{"text": "請提出解決方案"}]}, {"role": "model", "parts": [{"text": "好的，基於第一階段的深入分析，我為您設計了兩種核心解決方案。我將詳細說明每種方案的實作步驟、優點和缺點，並在最後提供一個比較摘要，以幫助您做出最適合專案的決策。\n\n---\n\n### **方案一：快取版本校驗機制 (建議方案)**\n\n這個方案的核心思想是：**保留現有的 Session 快取機制以維持高效能，但為快取加上一個「版本標記」。每次請求時，都快速檢查這個版本標記是否已過期，只有在過期時才重新從資料庫載入權限。**\n\n這是業界處理此類問題最成熟、最平衡的作法。\n\n#### **實作步驟：**\n\n1.  **資料庫層：新增版本標記**\n    *   **目標**：在儲存角色資訊的 `BSRole` 資料表中，增加一個時間戳記欄位，以便追蹤該角色的權限最後一次被修改的時間。\n    *   **動作**：修改 `BSRole` 資料表，新增一個名為 `LastModifiedDate` 的 `datetime2` 欄位。\n        ```sql\n        ALTER TABLE dbo.BSRole\n        ADD LastModifiedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE();\n        ```\n    *   **可選**：建立一個觸發器，在 `BSRoleFunction` (儲存角色與功能關聯的表) 有任何變動時，自動更新對應 `BSRole` 的 `LastModifiedDate`。這樣可以更精準地追蹤權限變更。\n\n2.  **登入邏輯：儲存版本標記**\n    *   **目標**：使用者登入時，不僅要讀取權限，還要讀取他們所屬所有角色的 `LastModifiedDate`，並將這些時間戳記存入 Session。\n    *   **動作**：\n        *   修改 `LoginUserTool` 所使用的使用者物件模型（可能在 `LoginUser` ViewModel 或類似類別中），新增一個屬性來儲存版本資訊，例如：`public Dictionary<int, DateTime> RoleVersions { get; set; }`。\n        *   修改 `HomeController` 的 `Login` Action 所呼叫的 `LoginService`，使其在取得使用者資料時，一併查詢其所有角色的 `ID` 和 `LastModifiedDate`，並存入上述的新屬性中。\n\n3.  **權限儲存邏輯：更新版本標記**\n    *   **目標**：當管理員在 `BSRoleController` 儲存權限變更時，必須更新對應角色的 `LastModifiedDate`。\n    *   **動作**：修改 `RoleService` 的 `SavePermission` 方法。在成功將權限變更儲存到資料庫後，必須執行一步額外的更新：\n        ```csharp\n        // 在儲存權限的核心邏輯後...\n        var role = RoleRepo.Get(roleId);\n        if (role != null)\n        {\n            role.LastModifiedDate = DateTime.UtcNow;\n            RoleRepo.Update(role);\n            UOW.Commit();\n        }\n        ```\n\n4.  **權限檢查核心：實現版本校驗 (最關鍵的一步)**\n    *   **目標**：修改 `MvcAuthFilter`，讓它在每次執行權限檢查前，先進行快速的版本校驗。\n    *   **動作**：在 `MvcAuthFilter` 的 `AuthorizeCore` 方法的開頭，加入以下邏輯：\n        1.  從 `LoginUserTool` 取得目前 Session 中儲存的角色版本 `RoleVersions`。\n        2.  執行一次**極快**的資料庫查詢，取得這些角色**目前**在資料庫中的 `LastModifiedDate`。\n        3.  比對 Session 中的版本和資料庫中的版本。\n        4.  **如果有任何一個角色的時間戳記不匹配**，就表示權限已過期。此時，立即呼叫 `LoginService` 中的一個新方法（例如 `ReloadPermissions`），重新從資料庫載入完整的權限和新的版本標記，並用 `LoginUserTool.SetLoginUser()` 更新整個 Session 中的使用者物件。\n        5.  如果版本全部匹配，則不做任何事，繼續使用 Session 中的快取權限，幾乎沒有效能耗損。\n\n#### **方案一優缺點**\n\n*   **優點**：\n    *   **高效能**：絕大多數請求都只增加一次極其微小的資料庫查詢，對系統效能影響極低。\n    *   **即時性**：權限變更後，使用者在下一次點擊任何頁面或按鈕時，權限就會立即更新。\n    *   **穩定可靠**：這是一個經過驗證的成熟模式，不易出錯。\n    *   **擴展性好**：即使未來系統部署在多台伺服器（Web Farm）上，此方案依然有效，因為狀態的最終來源是共享的資料庫。\n\n*   **缺點**：\n    *   **需要修改資料庫**：需要對 `BSRole` 表進行小幅修改。\n    *   **實作稍複雜**：需要在登入、儲存、驗證三個環節都進行修改，邏輯鏈條較長。\n\n---\n\n### **方案二：全域應用程式快取**\n\n這個方案的核心思想是：**將所有角色的權限資料從個別的 Session 中抽離出來，放到一個所有使用者共享的、存在於伺服器記憶體中的「全域快取」裡。**\n\n#### **實作步驟：**\n\n1.  **建立全域快取服務**\n    *   **目標**：建立一個靜態類別或單例模式的服務，用於管理權限快取。\n    *   **動作**：建立一個 `PermissionCacheService` 類別，內部使用 `System.Runtime.Caching.MemoryCache` 或一個 `ConcurrentDictionary<int, List<string>>` 來儲存每個 `roleId` 對應的權限列表。\n\n2.  **應用程式啟動時載入快取**\n    *   **目標**：在網站啟動時，預先將所有角色的權限資料載入到全域快取中。\n    *   **動作**：在 `Global.asax.cs` 的 `Application_Start` 方法中，呼叫 `PermissionCacheService` 的一個方法（例如 `LoadAllPermissions`），該方法會查詢資料庫，將所有角色的權限讀入記憶體。\n\n3.  **權限儲存邏輯：同步更新快取**\n    *   **目標**：當 `BSRoleController` 儲存權限時，除了更新資料庫，還必須**立即更新**全域快取中的資料。\n    *   **動作**：修改 `RoleService` 的 `SavePermission` 方法。在成功更新資料庫後，呼叫 `PermissionCacheService.UpdateRolePermissions(roleId, newTokens)` 來更新或清除快取中對應角色的條目。\n\n4.  **權限檢查核心：從全域快取讀取**\n    *   **目標**：修改 `MvcAuthFilter`，讓它不再從 Session 讀取權限，而是從全域快取中讀取。\n    *   **動作**：\n        *   修改 `MvcAuthFilter` 的 `AuthorizeCore` 方法。\n        *   它仍然需要從 Session 取得使用者的角色 ID (`roleId`)。\n        *   然後，使用 `roleId` 作為鍵，從 `PermissionCacheService` 中獲取權限列表。\n        *   用這個從全域快取中得到的權限列表來進行驗證。\n\n#### **方案二優缺點**\n\n*   **優點**：\n    *   **極致效能**：權限檢查完全在記憶體中進行，完全沒有資料庫讀取，速度最快。\n    *   **無需修改資料庫**：不需要對現有資料表進行任何修改。\n\n*   **缺點**：\n    *   **不支援多伺服器**：如果未來系統部署到多台伺服器（Web Farm/Cluster），此方案會立刻失效，因為每台伺服器的記憶體是獨立的。屆時需要引入 Redis 等分散式快取，大幅增加架構複雜度。\n    *   **需要處理並行問題**：對快取的讀寫需要加鎖（lock）或使用執行緒安全的集合，以避免多使用者同時操作時發生衝突，有潛在的風險。\n    *   **啟動延遲**：應用程式首次啟動時，需要一次性載入所有權限，可能會輕微增加啟動時間。\n\n---\n\n### **方案比較與總結**\n\n| 特性 | 方案一：快取版本校驗 (建議) | 方案二：全域應用程式快取 |\n| :--- | :--- | :--- |\n| **實作複雜度** | 中等 | 中等 |\n| **效能影響** | 極低 (每次請求多一次微小查詢) | 無 (純記憶體操作) |\n| **即時性** | 高 (下次請求生效) | 極高 (即時生效) |\n| **多伺服器支援** | **原生支援** | **不支援** (需重構成分散式快取) |\n| **資料庫變動** | **需要** (新增欄位) | 不需要 |\n| **可靠性** | 非常高 | 中等 (需處理執行緒安全) |\n\n**我的建議：**\n\n我強烈推薦**方案一（快取版本校驗機制）**。\n\n雖然它的實作步驟稍多，但它是一個更穩健、更具前瞻性的解決方案。它在提供高效能和即時性的同時，完美地避開了多伺服器部署和執行緒安全的陷阱。對於一個企業級的 ERP 系統來說，穩定性和未來的可擴展性至關重要。這個方案完全符合這些要求。\n\n請您評估這兩個方案。一旦您做出決定，我就可以為您制定更詳細的程式碼修改計畫，或在您同意的情況下開始實作您選擇的方案。"}]}]