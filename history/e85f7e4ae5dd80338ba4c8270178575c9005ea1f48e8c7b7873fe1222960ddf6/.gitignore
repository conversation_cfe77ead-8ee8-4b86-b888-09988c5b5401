# 環境設定
.env
.env.local
.env.*.local

# 快取資料
cache/
*.cache

# 日誌檔案
logs/
*.log

# 輸出檔案（視需求決定是否追蹤）
outputs/*.md
outputs/*.html
outputs/*.pdf

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# 暫存檔案
*.tmp
*.temp
.~*

# 敏感資料
context/current-review/*
!context/current-review/.gitkeep
tokens/
credentials/
context/pull-requests/
