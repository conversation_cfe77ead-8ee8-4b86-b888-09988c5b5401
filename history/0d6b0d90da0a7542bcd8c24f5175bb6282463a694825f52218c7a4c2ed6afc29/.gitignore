# 環境設定
.env
.env.local
.env.*.local

# Claude 本地設定
.claude/settings.local.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv
*.egg-info/
dist/
build/
*.egg

# 虛擬環境
research/tfs-api/venv/
research/*/venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# 測試和暫存檔案
*.log
*.tmp
*.temp
*.cache
.pytest_cache/
.coverage
htmlcov/
.tox/

# 輸出檔案
# research/*/output/
# research/chrome-history/output/
# research/*/api_responses/
# *.json
# !sample_*.json
# !project_client_mapping.json

# 報告檔案
# *_report_*.md
# *_report_*.json
# *_report_*.txt
# *_report_*.html
# *_report_*.pdf
# daily_report_*.json
# complete_daily_report_*.json
# complete_daily_report_*.md
# simple_report_*.txt
# statistics_*.json

# 敏感資料
credentials/
secrets/
*.key
*.pem
*.cert

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# 備份檔案
*.bak
*.backup
*.old

# 資料庫
*.db
*.sqlite
*.sqlite3

# Node.js (如果未來需要)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
output/ai_prompt*
output/2025*
research/activitywatch/api_responses/
research/chrome-history/output/chrome_work_report_2025-06-03.json
research/chrome-history/output/chrome_work_report_2025-06-03.md
